{"functions": {"api/index.js": {"included_files": ["index.js"], "runtime": "nodejs18.x"}}, "redirects": [{"source": "/(.*)", "destination": "/api/index.js", "headers": {"Access-Control-Allow-Credentials": "true", "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS", "Access-Control-Allow-Headers": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version"}, "status": 200}]}