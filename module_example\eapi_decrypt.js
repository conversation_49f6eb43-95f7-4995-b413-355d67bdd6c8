const { eapiResDecrypt, eapiReqDecrypt } = require('../util/crypto')

let reqParamsHex =
  '6E0B1C712DCB3648589D7C950C296204072A88C3E080C4CFFD0A71A553EC2533BA88E11B1E1C6DF3BE8EFA26177FCB6FCA34EB3FAFAB4671B2BBAFA9781AFDA2BF53A3DC423722493837B9BC6E80CED5BBD2DDC2856920E4D4E3E7F3EB77ECF265217A66AE677BE36F2D6FB203F721CA250E1453EA61A34904E33D5FCB9D483601D744BE0AE979AC911A00F25828538844F4B1C24F6C34880A4AB257F530C7FB10A81FED32B18D09D70C0B1B9D34A2E58A3C3FAD382C6F958077059C4F801AD7B3B248FDB9D7A59B6A9EEFF8C781A84315B33A7AFD48BE9FCFCBE1902CCC27949ACF2BDE3FA34D116E230C3597E8320B8C42BBBF371A00C03EC428E0440EB94C1540F3FD4173D29E310AFE43AB0EF449852904103EF305FC435AD43B7D8673642F74C89CCB2F1A6A79B3BE14F1235D3843C3B241D12C05DBDDF37B68CA8B5D0230AF1FCF2A9705886F4D126B33FFF6948DE1E4046DB6423D687E96C5B65122464D2E71AEC7722935FF2C3796FAE253A16AA3B102FBE7296AB0DB9EA5C46AD12B'.replaceAll(
    ' ',
    '',
  )
const resHex =
  `FB A2 DB 80 DF 26 69 F1 AA 14 81 8E 56 93 53 A6 44 9C 10 70 26 65 59 64 AE 09 02 75 56 65 1C 98 E6 DC E7 50 F1 6E 64 78 BC 87 B8 0E 6D 52 B0 23`.replaceAll(
    ' ',
    '',
  )
console.log(reqParamsHex)
console.log(eapiReqDecrypt(reqParamsHex))
console.log(eapiResDecrypt(resHex))
