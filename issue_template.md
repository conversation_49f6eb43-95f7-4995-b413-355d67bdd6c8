## 环境
- 系统/平台: <你的系统和平台>

- nodejs 版本: <你的 NodeJS 版本号>

- API版本:<运行的云音乐 API 的版本号， 对应 package.json 里面的 version>

## 出现问题
<出现的问题>

## 重现步骤
<重现步骤>

## 期待效果
<现在的效果，期待的效果>  



>先看文档有没有相关说明,调用前须知必看

>先在 issues 搜一下是否有相似问题，没有再发,否则直接关闭

>不处理别人搭建的线上服务的问题,此项目不提供任何线上服务,请自行搭建

>重现步骤尽量详细,不能含糊不清，包含请求地址和对应参数以及操作过程描述,不是每个人都喜欢猜别人遇到了什么问题和找参数一个个试,也比较浪费时间

>如果不是提建议,提 issues 如果不照着模版来将不会优先处理或直接关闭

>460 cheating 的问题把 `util/request.js` 里面的 `headers['X-Real-IP']` 的注释取消掉就好
