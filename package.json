{"name": "@neteaseapireborn/api", "version": "4.28.24", "description": "为停更的网易云音乐 NodeJs API 提供持续的维护！", "scripts": {"start": "node app.js", "test": "mocha -r intelli-espower-loader -t 60000 server.test.js main.test.js --exit", "lint": "eslint \"**/*.{js,ts}\"", "lint-fix": "eslint --fix \"**/*.{js,ts}\"", "prepare": "husky install", "pkgwin": "pkg . -t node18-win-x64 -C GZip -o bin/app --no-bytecode", "pkglinux": "pkg . -t node18-linux-x64 -C GZip -o bin/app --no-bytecode", "pkgmacos": "pkg . -t node18-macos-x64 -C GZip -o bin/app --no-bytecode"}, "bin": "./app.js", "pkg": {"scripts": "module/*.js", "assets": ["node_modules/axios", "node_modules/express", "node_modules/express-fileupload", "node_modules/md5", "node_modules/music-metadata", "node_modules/pac-proxy-agent", "node_modules/qrcode", "node_modules/safe-decode-uri-component", "node_modules/tunnel", "node_modules/yargs", "node_modules/tslib", "node_modules/vm2", "module", "public", "data"]}, "keywords": ["网易云音乐", "网易云", "音乐", "网易云音乐nodejs", "API", "网易云音乐APIReborn", "网易云音乐APIEnhanced"], "main": "main.js", "types": "./interface.d.ts", "engines": {"node": ">=12"}, "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "author": "binaryify", "license": "MIT", "files": ["module", "util", "plugins", "main.d.ts", "interface.d.ts", "module_types", "server.js", "generateConfig.js", "public", "data"], "dependencies": {"@unblockneteasemusic/server": "^0.27.10", "axios": "^1.11.0", "crypto-js": "^4.2.0", "dotenv": "^16.6.1", "express": "^4.21.2", "express-fileupload": "^1.5.2", "md5": "^2.3.0", "music-metadata": "^7.14.0", "node-forge": "^1.3.1", "pac-proxy-agent": "^7.2.0", "qrcode": "^1.5.4", "safe-decode-uri-component": "^1.2.1", "tunnel": "^0.0.6", "xml2js": "^0.6.2", "yargs": "^17.7.2"}, "devDependencies": {"@types/express": "^4.17.23", "@types/express-fileupload": "^1.5.1", "@types/mocha": "^9.1.1", "@types/node": "16.11.19", "@typescript-eslint/eslint-plugin": "5.0.0", "@typescript-eslint/parser": "5.0.0", "eslint": "8.7.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-html": "7.1.0", "eslint-plugin-prettier": "4.0.0", "husky": "7.0.4", "intelli-espower-loader": "1.1.0", "lint-staged": "12.1.7", "mocha": "10.0.0", "pkg": "^5.8.1", "power-assert": "1.6.1", "prettier": "2.7.1", "typescript": "4.5.2"}}